<!--pages/product/product_library/product_library.wxml-->
<view class="container p-20">
  <!-- 筛选区域 -->
  <view class="filter-section card mb-20">
    <view class="card-header">
      <view class="filter-title card-title">产品筛选</view>
    </view>
    
    <view class="card-body">
      <!-- 产品类型选择 -->
      <view class="filter-row mb-30">
        <view class="filter-label form-label">产品类型</view>
        <view class="filter-options mb-10">
          <view 
            class="option-item {{item.value === selectedProductType ? 'selected' : ''}}"
            wx:for="{{productTypeOptions}}" 
            wx:key="value"
            data-value="{{item.value}}"
            data-index="{{index}}"
            bindtap="onProductTypeSelect"
          >
            {{item.label}}
          </view>
        </view>
      </view>

      <!-- 品牌选择 -->
      <view class="filter-row mb-30">
        <view class="filter-label form-label">品牌</view>
        <view class="filter-options {{brandOptions.length === 0 ? 'disabled' : ''}} mb-10">
          <view 
            class="option-item {{item.value === selectedBrand ? 'selected' : ''}}"
            wx:for="{{brandOptions}}" 
            wx:key="value"
            data-value="{{item.value}}"
            data-index="{{index}}"
            bindtap="onBrandSelect"
          >
            {{item.label}}
          </view>
          <view class="no-options" wx:if="{{brandOptions.length === 0}}">
            请先选择产品类型
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="filter-buttons mt-30">
        <button class="btn btn-primary btn-medium btn-full" bindtap="searchProducts" disabled="{{loading}}">
          {{loading ? '搜索中...' : '搜索产品'}}
        </button>
        <button class="btn btn-secondary btn-medium btn-full" bindtap="resetFilters">重置筛选</button>
      </view>
    </view>
  </view>

  <!-- 产品列表区域 -->
  <view class="products-section card">
    <view class="card-header">
      <view class="products-header mb-30 pb-20">
        <view class="products-title card-title">产品列表</view>
        <view class="products-count" wx:if="{{products.length > 0 || totalCount > 0}}">
          共{{totalCount || 0}}个产品
        </view>
      </view>
    </view>

    <view class="card-body">
      <!-- 加载状态 -->
      <view class="loading-container py-60" wx:if="{{loading && products.length === 0}}">
        <loading size="24" color="#3B7ADB"></loading>
        <text class="loading-text ml-20">搜索中...</text>
      </view>

      <!-- 产品列表滚动容器 -->
      <scroll-view 
        class="products-scroll-view {{products.length === 0 && !loading ? 'empty-scroll-view' : ''}}"
        scroll-y="{{true}}"
        lower-threshold="{{100}}"
        scroll-top="{{scrollTop}}"
        enable-back-to-top="{{true}}"
        enhanced="{{true}}"
        show-scrollbar="{{false}}"
        wx:if="{{!loading || products.length > 0}}"
      >
        <!-- 产品列表 -->
        <view class="products-list" wx:if="{{products.length > 0}}">
          <product-show
            wx:for="{{products}}"
            wx:key="{{item.skuId || index}}"
            product="{{item}}"
            custom-class="product-item mb-20"
            bind:productTap="onProductTap"
            bind:imageError="onImageError"
          />
        </view>

        <!-- 底部加载状态和操作区域 -->
        <view class="load-more-container py-40" wx:if="{{products.length > 0}}">
          <!-- 查看更多产品文字链接 -->
          <view class="load-more-text-container" wx:if="{{hasMore && !loadingMore}}">
            <view class="load-more-info">
              <text class="remaining-count">还有{{totalCount - products.length > 0 ? totalCount - products.length : 0}}个产品未显示</text>
            </view>
            <view 
              class="load-more-link"
              bindtap="onLoadMoreClick"
            >
              <text class="load-more-text">查看更多产品</text>
              <text class="load-more-arrow">↓</text>
            </view>
          </view>
          
          <!-- 加载中状态 -->
          <view class="load-more-loading py-20" wx:if="{{loadingMore}}">
            <loading size="20" color="#3B7ADB"></loading>
            <text class="ml-20">正在加载更多产品...</text>
          </view>
          
          <!-- 没有更多数据 -->
          <view class="no-more py-20" wx:if="{{!hasMore && !loadingMore}}">
            <view class="no-more-icon">✨</view>
            <text class="no-more-text">已显示全部{{totalCount}}个产品</text>
          </view>
        </view>

        <!-- 空状态 - 移到 scroll-view 内部 -->
        <view class="empty-container" wx:if="{{products.length === 0 && hasSearched && !loading}}">
          <view class="empty-icon mb-20">📱</view>
          <view class="empty-title mb-10">暂无相关产品</view>
          <view class="empty-desc">请尝试调整筛选条件重新搜索</view>
        </view>

        <!-- 初始状态 - 移到 scroll-view 内部 -->
        <view class="empty-container welcome-container" wx:if="{{!loading && products.length === 0 && !hasSearched}}">
          <view class="welcome-icon mb-20">🔍</view>
          <view class="welcome-title mb-10">欢迎使用产品库</view>
          <view class="welcome-desc mb-30">请选择产品类型和品牌进行搜索</view>
        </view>
      </scroll-view>
    </view>
  </view>
</view>
