<!--components/product-show/product-show.wxml-->
<view 
  class="product-show {{customClass}} {{disabled ? 'disabled' : ''}} {{loading ? 'loading' : ''}}"
  bindtap="onProductTap"
>
  <!-- 加载状态 -->
  <view class="product-loading" wx:if="{{loading}}">
    <view class="loading-skeleton">
      <view class="skeleton-image"></view>
      <view class="skeleton-content">
        <view class="skeleton-line skeleton-title"></view>
        <view class="skeleton-line skeleton-meta"></view>
      </view>
    </view>
  </view>

  <!-- 产品内容 -->
  <view class="product-content" wx:else>
    <!-- 产品图片 -->
    <view class="product-image-container">
      <image 
        class="product-image" 
        src="{{imageError ? defaultImage : (product.imageUrl || defaultImage)}}" 
        mode="aspectFill"
        lazy-load="{{true}}"
        binderror="onImageError"
        bindload="onImageLoad"
      ></image>
      
      <!-- 图片加载失败提示 -->
      <view class="image-error-overlay" wx:if="{{imageError}}">
        <text class="error-icon">📷</text>
        <text class="error-text">图片加载失败</text>
      </view>
    </view>

    <!-- 产品信息 -->
    <view class="product-info">
      <!-- 产品名称 -->
      <view class="product-name">
        {{product.skuName || '未知产品'}}
      </view>
      
      <!-- 产品元信息 -->
      <view class="product-meta">
        <!-- 发布日期 -->
        <view class="product-date" wx:if="{{product.releaseDate}}">
          {{product.releaseDate}}
        </view>
        
        <!-- 产品类型 -->
        <view class="product-type" wx:if="{{product.productType}}">
          {{product.productType}}
        </view>
      </view>
    </view>

    <!-- 点击反馈效果 -->
    <view class="tap-feedback" wx:if="{{!disabled && !loading}}"></view>
  </view>
</view>
