/**
 * 产品展示组件
 * Product Show Component
 * 用于展示产品信息，包括图片、名称、发布日期、产品类型等
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 产品数据
    product: {
      type: Object,
      value: {}
    },
    // 是否显示加载状态
    loading: {
      type: Boolean,
      value: false
    },
    // 自定义样式类名
    customClass: {
      type: String,
      value: ''
    },
    // 是否禁用点击
    disabled: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 默认图片
    defaultImage: '/assets/images/default-product.png',
    // 图片加载失败标记
    imageError: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 产品点击事件
     */
    onProductTap(e) {
      if (this.data.disabled || this.data.loading) {
        return;
      }

      const product = this.data.product;
      console.log('产品展示组件 - 点击产品:', product);

      // 触发父组件的产品点击事件
      this.triggerEvent('productTap', {
        product: product
      });
    },

    /**
     * 图片加载错误处理
     */
    onImageError(e) {
      console.log('产品图片加载失败:', e);
      this.setData({
        imageError: true
      });

      // 触发父组件的图片错误事件
      this.triggerEvent('imageError', {
        product: this.data.product,
        error: e
      });
    },

    /**
     * 图片加载成功处理
     */
    onImageLoad(e) {
      console.log('产品图片加载成功:', e);
      this.setData({
        imageError: false
      });

      // 触发父组件的图片加载成功事件
      this.triggerEvent('imageLoad', {
        product: this.data.product,
        event: e
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    /**
     * 组件实例刚刚被创建时执行
     */
    created() {
      console.log('产品展示组件创建');
    },

    /**
     * 组件实例进入页面节点树时执行
     */
    attached() {
      console.log('产品展示组件附加到页面');
    },

    /**
     * 组件实例被从页面节点树移除时执行
     */
    detached() {
      console.log('产品展示组件从页面移除');
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'product'(newProduct) {
      if (newProduct) {
        // 重置图片错误状态
        this.setData({
          imageError: false
        });
        console.log('产品展示组件 - 产品数据更新:', newProduct);
      }
    }
  }
});
