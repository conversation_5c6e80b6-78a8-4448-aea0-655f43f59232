# 产品展示组件 (Product Show Component)

## 概述
产品展示组件用于在产品库页面展示产品信息，包括产品图片、名称、发布日期和产品类型等。

## 功能特性
- 📱 产品信息展示（图片、名称、发布日期、产品类型）
- 🎨 优雅的加载骨架屏动画
- 🖼️ 图片加载失败处理和默认图片
- 📱 响应式设计，适配不同屏幕尺寸
- ⚡ 点击反馈效果和禁用状态支持
- 🎯 事件回调支持

## 使用方法

### 1. 在页面配置文件中注册组件
```json
{
  "usingComponents": {
    "product-show": "../../components/product-show/product-show"
  }
}
```

### 2. 在WXML中使用组件
```xml
<!-- 基础使用 -->
<product-show 
  product="{{productData}}"
  bind:productTap="onProductTap"
  bind:imageError="onImageError"
/>

<!-- 带自定义样式类 -->
<product-show 
  product="{{productData}}"
  custom-class="my-custom-style"
  bind:productTap="onProductTap"
/>

<!-- 加载状态 -->
<product-show 
  loading="{{true}}"
/>

<!-- 禁用状态 -->
<product-show 
  product="{{productData}}"
  disabled="{{true}}"
/>
```

### 3. 在JS中处理事件
```javascript
Page({
  data: {
    productData: {
      skuId: 'product_001',
      skuName: '华为 Mate 70 Pro',
      imageUrl: 'https://example.com/product-image.jpg',
      releaseDate: '2024-01-15',
      productType: '手机'
    }
  },

  // 产品点击事件
  onProductTap(e) {
    const product = e.detail.product;
    console.log('点击产品:', product);
    
    // 跳转到产品详情页
    wx.navigateTo({
      url: `/pages/product/detail?id=${product.skuId}`
    });
  },

  // 图片加载错误事件
  onImageError(e) {
    const product = e.detail.product;
    console.log('图片加载失败:', product);
  }
});
```

## 属性 (Properties)

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| product | Object | {} | 是 | 产品数据对象 |
| loading | Boolean | false | 否 | 是否显示加载状态 |
| customClass | String | '' | 否 | 自定义样式类名 |
| disabled | Boolean | false | 否 | 是否禁用点击 |

## 产品数据结构 (Product Data Structure)

```javascript
{
  skuId: 'string',        // 产品ID（用于wx:key）
  skuName: 'string',      // 产品名称
  imageUrl: 'string',     // 产品图片URL
  releaseDate: 'string',  // 发布日期
  productType: 'string'   // 产品类型
}
```

## 事件 (Events)

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| productTap | 产品点击事件 | { product: Object } |
| imageError | 图片加载失败事件 | { product: Object, error: Object } |
| imageLoad | 图片加载成功事件 | { product: Object, event: Object } |

## 样式定制

组件支持通过 `custom-class` 属性传入自定义样式类名：

```css
/* 自定义间距 */
.my-product-item {
  margin-bottom: 30rpx;
}

/* 自定义背景色 */
.my-product-item {
  background-color: #ffffff;
}
```

## 注意事项

1. 确保传入的产品数据包含必要的字段
2. 图片URL应该是有效的网络地址或本地路径
3. 组件会自动处理图片加载失败的情况
4. 在列表中使用时，建议设置合适的 `wx:key` 值

## 更新日志

### v1.0.0 (2024-07-24)
- 初始版本发布
- 支持基础产品信息展示
- 支持加载状态和错误处理
- 支持响应式设计
