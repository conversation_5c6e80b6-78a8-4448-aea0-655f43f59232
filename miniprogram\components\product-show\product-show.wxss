/**
 * 产品展示组件样式
 * Product Show Component Styles
 * 依赖：全局样式系统（variables.wxss, components.wxss, utilities.wxss）
 */

/* ==================== 组件容器 Component Container ==================== */
.product-show {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  overflow: hidden;
  padding: 20rpx;
  box-sizing: border-box;
}

.product-show:active {
  transform: translateY(2rpx);
  border-color: #3B7ADB;
  box-shadow: 0 4rpx 12rpx rgba(59, 122, 219, 0.15);
}

/* 禁用状态 */
.product-show.disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 加载状态 */
.product-show.loading {
  pointer-events: none;
}

/* ==================== 加载骨架屏 Loading Skeleton ==================== */
.product-loading {
  width: 100%;
}

.loading-skeleton {
  display: flex;
  align-items: center;
  width: 100%;
}

.skeleton-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.skeleton-line {
  height: 24rpx;
  border-radius: 12rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-title {
  width: 80%;
  height: 32rpx;
}

.skeleton-meta {
  width: 60%;
  height: 24rpx;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* ==================== 产品内容 Product Content ==================== */
.product-content {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
}

/* ==================== 产品图片 Product Image ==================== */
.product-image-container {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.product-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  background: #f8f9fa;
  border: 2rpx solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图片错误覆盖层 */
.image-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(248, 249, 250, 0.9);
  border-radius: 8rpx;
  border: 2rpx solid #e6e6e6;
}

.error-icon {
  font-size: 32rpx;
  color: #ccc;
  margin-bottom: 4rpx;
}

.error-text {
  font-size: 20rpx;
  color: #999;
  text-align: center;
}

/* ==================== 产品信息 Product Info ==================== */
.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0; /* 防止flex子项溢出 */
}

.product-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-meta {
  display: flex;
  gap: 20rpx;
  align-items: center;
  flex-wrap: wrap;
}

/* ==================== 产品元信息 Product Meta ==================== */
.product-date {
  font-size: 24rpx;
  color: #666;
  background: white;
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  border: 1rpx solid #e6e6e6;
  white-space: nowrap;
}

.product-type {
  font-size: 24rpx;
  color: #3B7ADB;
  background: rgba(59, 122, 219, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  white-space: nowrap;
}

/* ==================== 点击反馈效果 Tap Feedback ==================== */
.tap-feedback {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  border-radius: 12rpx;
  transition: background-color 0.1s ease;
}

.product-show:active .tap-feedback {
  background-color: rgba(59, 122, 219, 0.05);
}

/* ==================== 响应式适配 Responsive Design ==================== */
@media (max-width: 750rpx) {
  .product-show {
    flex-direction: column;
    text-align: center;
    padding: 24rpx;
  }
  
  .product-image-container {
    margin-right: 0;
    margin-bottom: 16rpx;
  }
  
  .product-meta {
    justify-content: center;
  }
}
