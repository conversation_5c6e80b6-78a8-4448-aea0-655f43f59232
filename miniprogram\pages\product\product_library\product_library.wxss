/**
 * 产品库页面样式
 * Product Library Page Styles
 * 依赖：全局样式系统（variables.wxss, components.wxss, utilities.wxss）
 */

/* ==================== 页面容器 Page Container ==================== */
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* ==================== 页面特有样式覆盖 Page-specific Overrides ==================== */

/* 筛选区域特殊样式 */
.filter-title::before,
.products-title::before {
  content: '';
  width: 6rpx;
  height: 24rpx;
  background: linear-gradient(135deg, #3B7ADB, #5B9BD5);
  border-radius: 3rpx;
  margin-right: 12rpx;
}

/* 产品列表头部样式调整 */
.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2rpx solid #f0f0f0;
}

.products-count {
  font-size: 24rpx;
  color: #666;
  background: #f8f9fa;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

/* ==================== 筛选选项组件 Filter Options Component ==================== */
.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.filter-options.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 筛选选项按钮 */
.option-item {
  padding: 16rpx 24rpx;
  border: 2rpx solid #e6e6e6;
  border-radius: 8rpx;
  background-color: #fff;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 120rpx;
  text-align: center;
}

.option-item:active {
  transform: scale(0.95);
}

.option-item.selected {
  background: linear-gradient(135deg, #3B7ADB, #5B9BD5);
  border-color: #3B7ADB;
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(59, 122, 219, 0.3);
}

/* 无选项状态 */
.no-options {
  padding: 20rpx;
  text-align: center;
  color: #999;
  font-size: 26rpx;
  width: 100%;
  background: #f8f9fa;
  border-radius: 8rpx;
  border: 2rpx dashed #e6e6e6;
}

/* ==================== 筛选按钮区域 Filter Buttons ==================== */
.filter-buttons {
  display: flex;
  gap: 20rpx;
}

/* ==================== 产品列表组件 Products List Component ==================== */
.products-list {
  display: flex;
  flex-direction: column;
  gap: 0; /* 移除gap，因为组件内部已经有margin */
}

/* ==================== 状态组件 State Components ==================== */
/* 加载状态 - 使用全局间距工具类 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
  min-height: 300rpx;
}

.empty-icon {
  font-size: 100rpx;
  color: #ddd;
  margin-bottom: 20rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
}

/* ==================== scroll-view 滚动容器样式 ==================== */
.products-scroll-view {
  height: calc(100vh - 600rpx); /* 根据筛选区域高度调整 */
  min-height: 400rpx;
}

/* 空状态时的 scroll-view 样式 */
.empty-scroll-view {
  height: auto;
  min-height: 300rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ==================== 欢迎页面样式优化 ==================== */
.welcome-container {
  padding: 60rpx 40rpx;
  min-height: 300rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.welcome-icon {
  font-size: 100rpx;
  margin-bottom: 24rpx;
  animation: welcome-pulse 2s infinite;
}

@keyframes welcome-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.welcome-title {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.welcome-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  text-align: center;
  line-height: 1.6;
}

.welcome-features {
  display: flex;
  gap: 30rpx;
  justify-content: center;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #f8f9ff, #ffffff);
  border-radius: 16rpx;
  border: 1rpx solid #e8ecf4;
  min-width: 120rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.feature-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.feature-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

/* ==================== 加载更多文字链接区域 Load More Text Link Area ==================== */
.load-more-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 0;
}

.load-more-info {
  margin-bottom: 8rpx;
}

.remaining-count {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  opacity: 0.8;
}

.load-more-link {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 20rpx;
  background: rgba(59, 122, 219, 0.05);
}

.load-more-link:active {
  transform: scale(0.96);
  background: rgba(59, 122, 219, 0.1);
}

.load-more-text {
  font-size: 28rpx;
  color: #3B7ADB;
  font-weight: 500;
}

.load-more-arrow {
  font-size: 20rpx;
  color: #3B7ADB;
  animation: bounce-gentle 2s infinite;
}

@keyframes bounce-gentle {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3rpx);
  }
  60% {
    transform: translateY(-2rpx);
  }
}

/* ==================== 响应式适配 Responsive Design ==================== */
@media (max-width: 750rpx) {
  .filter-buttons {
    flex-direction: column;
  }
}
